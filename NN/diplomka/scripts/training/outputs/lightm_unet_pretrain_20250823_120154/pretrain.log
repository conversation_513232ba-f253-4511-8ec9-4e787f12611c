=== LightM-UNet Pretraining Configuration ===
Model: lightm_unet
Dataset: /data/prusek/training_big
Output: ./outputs/lightm_unet_pretrain_20250823_120154
Batch size: 1
Image size: 1024
Epochs: 150
Learning rate: 1e-4
Weight decay: 1e-5
Optimizer: adamw
Scheduler: cosine
GPUs: 2
Workers: 8
Patience: 25
=============================================
Starting pretraining...
============================================================
COMPLETE DATASET SUMMARY
============================================================
Train samples: 28,778
Val samples:   2,539
Test samples:  653
Total samples: 31,970
============================================================

TRAIN Dataset Summary:
  Total images found: 28778
  Valid image-mask pairs: 28778
  Images without masks: 0
  Final train dataset size: 28778

VAL Dataset Summary:
  Total images found: 2539
  Valid image-mask pairs: 2539
  Images without masks: 0
  Final val dataset size: 2539

Running Learning Rate Finder...

TRAIN Dataset Summary:
  Total images found: 28778
  Valid image-mask pairs: 28778
  Images without masks: 0
  Final train dataset size: 28778

VAL Dataset Summary:
  Total images found: 2539
  Valid image-mask pairs: 2539
  Images without masks: 0
  Final val dataset size: 2539

TEST Dataset Summary:
  Total images found: 653
  Valid image-mask pairs: 653
  Images without masks: 0
  Final test dataset size: 653

============================================================
COMPLETE DATASET SUMMARY
============================================================
Dataset path: /data/prusek/training_big
Image size: 1024x1024
------------------------------------------------------------
Train samples: 28,778
Val samples:   2,539
Test samples:  653
------------------------------------------------------------
Total samples: 31,970
============================================================


Running Learning Rate Finder...

Finding LR:   0%|          | 0/100 [00:00<?, ?it/s]
Finding LR:   0%|          | 0/100 [00:00<?, ?it/s]/home/<USER>/.local/lib/python3.9/site-packages/torch/autograd/graph.py:824: UserWarning: Grad strides do not match bucket view strides. This may indicate grad was not created according to the gradient layout contract, or that the param's strides changed since DDP was constructed.  This is not an error, but may impair performance.
grad.sizes() = [32, 96, 1, 1], strides() = [96, 1, 96, 96]
bucket_view.sizes() = [32, 96, 1, 1], strides() = [96, 1, 1, 1] (Triggered internally at /pytorch/torch/csrc/distributed/c10d/reducer.cpp:328.)
  return Variable._execution_engine.run_backward(  # Calls into the C++ engine to run the backward pass
/home/<USER>/.local/lib/python3.9/site-packages/torch/autograd/graph.py:824: UserWarning: Grad strides do not match bucket view strides. This may indicate grad was not created according to the gradient layout contract, or that the param's strides changed since DDP was constructed.  This is not an error, but may impair performance.
grad.sizes() = [32, 96, 1, 1], strides() = [96, 1, 96, 96]
bucket_view.sizes() = [32, 96, 1, 1], strides() = [96, 1, 1, 1] (Triggered internally at /pytorch/torch/csrc/distributed/c10d/reducer.cpp:328.)
  return Variable._execution_engine.run_backward(  # Calls into the C++ engine to run the backward pass

Finding LR:   1%|          | 1/100 [00:01<02:03,  1.24s/it]
Finding LR:   1%|          | 1/100 [00:01<01:58,  1.20s/it]
Finding LR:   1%|          | 1/100 [00:01<01:59,  1.20s/it]

Finding LR:   1%|          | 1/100 [00:01<02:03,  1.25s/it]
[rank0]:[W823 12:02:32.517949971 ProcessGroupNCCL.cpp:1479] Warning: WARNING: destroy_process_group() was not called before program exit, which can leak resources. For more info, please see https://pytorch.org/docs/stable/distributed.html#shutdown (function operator())
W0823 12:02:33.439003 3441540 torch/multiprocessing/spawn.py:169] Terminating process 3441702 via signal SIGTERM
Traceback (most recent call last):
  File "/home/<USER>/SpheroSeg/NN/diplomka/scripts/training/../../CNN_main_spheroid.py", line 1486, in <module>
    main()
  File "/home/<USER>/SpheroSeg/NN/diplomka/scripts/training/../../CNN_main_spheroid.py", line 1481, in main
    mp.spawn(train, args=(world_size, args), nprocs=world_size, join=True)
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/multiprocessing/spawn.py", line 340, in spawn
    return start_processes(fn, args, nprocs, join, daemon, start_method="spawn")
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/multiprocessing/spawn.py", line 296, in start_processes
    while not context.join():
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/multiprocessing/spawn.py", line 215, in join
    raise ProcessRaisedException(msg, error_index, failed_process.pid)
torch.multiprocessing.spawn.ProcessRaisedException: 

-- Process 1 terminated with the following error:
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/multiprocessing/spawn.py", line 90, in _wrap
    fn(i, *args)
  File "/home/<USER>/SpheroSeg/NN/diplomka/CNN_main_spheroid.py", line 1257, in train
    lr_history = lr_finder.find(train_loader, start_lr=1e-7, end_lr=1, num_iter=100)
  File "/home/<USER>/SpheroSeg/NN/diplomka/CNN_main_spheroid.py", line 765, in find
    outputs = self.model(images)
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/nn/parallel/distributed.py", line 1633, in forward
    inputs, kwargs = self._pre_forward(*inputs, **kwargs)
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/nn/parallel/distributed.py", line 1522, in _pre_forward
    if torch.is_grad_enabled() and self.reducer._rebuild_buckets():
RuntimeError: Expected to have finished reduction in the prior iteration before starting a new one. This error indicates that your module has parameters that were not used in producing loss. You can enable unused parameter detection by passing the keyword argument `find_unused_parameters=True` to `torch.nn.parallel.DistributedDataParallel`, and by 
making sure all `forward` function outputs participate in calculating loss. 
If you already have done the above, then the distributed data parallel module wasn't able to locate the output tensors in the return value of your module's `forward` function. Please include the loss function and the structure of the return value of `forward` of your module when reporting this issue (e.g. list, dict, iterable).
Parameter indices which did not receive grad for rank 1: 7 8 13 14 34 35 40 41 57 58 63 64 84 85 90 91 107 108 113 114 134 135 140 141 157 158 163 164 180 181 186 187 203 204 209 210 230 231 236 237 253 254 259 260 280 281 286 287 303 304 309 310 330 331 336 337
 In addition, you can set the environment variable TORCH_DISTRIBUTED_DEBUG to either INFO or DETAIL to print out information about which particular parameters did not receive gradient on this rank as part of this error

=== Pretraining Completed Successfully ===
Model saved in: ./outputs/lightm_unet_pretrain_20250823_120154
Best model checkpoint: ./outputs/lightm_unet_pretrain_20250823_120154/best_model.pth
Training log: ./outputs/lightm_unet_pretrain_20250823_120154/pretrain.log
=========================================
