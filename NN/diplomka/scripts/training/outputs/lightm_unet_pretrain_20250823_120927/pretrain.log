=== LightM-UNet Pretraining Configuration ===
Model: lightm_unet
Dataset: /data/prusek/training_big
Output: ./outputs/lightm_unet_pretrain_20250823_120927
Batch size: 2
Image size: 1024
Epochs: 150
Learning rate: 1e-4
Weight decay: 1e-5
Optimizer: adamw
Scheduler: cosine
GPUs: 2
Workers: 8
Patience: 25
=============================================
Starting pretraining...
============================================================
COMPLETE DATASET SUMMARY
============================================================
Train samples: 28,778
Val samples:   2,539
Test samples:  653
Total samples: 31,970
============================================================

TRAIN Dataset Summary:
  Total images found: 28778
  Valid image-mask pairs: 28778
  Images without masks: 0
  Final train dataset size: 28778

VAL Dataset Summary:
  Total images found: 2539
  Valid image-mask pairs: 2539
  Images without masks: 0
  Final val dataset size: 2539

Running Learning Rate Finder...

TRAIN Dataset Summary:
  Total images found: 28778
  Valid image-mask pairs: 28778
  Images without masks: 0
  Final train dataset size: 28778

VAL Dataset Summary:
  Total images found: 2539
  Valid image-mask pairs: 2539
  Images without masks: 0
  Final val dataset size: 2539

TEST Dataset Summary:
  Total images found: 653
  Valid image-mask pairs: 653
  Images without masks: 0
  Final test dataset size: 653

============================================================
COMPLETE DATASET SUMMARY
============================================================
Dataset path: /data/prusek/training_big
Image size: 1024x1024
------------------------------------------------------------
Train samples: 28,778
Val samples:   2,539
Test samples:  653
------------------------------------------------------------
Total samples: 31,970
============================================================


Running Learning Rate Finder...

Finding LR:   0%|          | 0/100 [00:00<?, ?it/s]
Finding LR:   0%|          | 0/100 [00:00<?, ?it/s]
Finding LR:   0%|          | 0/100 [00:00<?, ?it/s]

Finding LR:   0%|          | 0/100 [00:00<?, ?it/s]
[rank0]:[W823 12:10:05.830450051 ProcessGroupNCCL.cpp:1479] Warning: WARNING: destroy_process_group() was not called before program exit, which can leak resources. For more info, please see https://pytorch.org/docs/stable/distributed.html#shutdown (function operator())
W0823 12:10:06.691985 3455082 torch/multiprocessing/spawn.py:169] Terminating process 3455231 via signal SIGTERM
Traceback (most recent call last):
  File "/home/<USER>/SpheroSeg/NN/diplomka/scripts/training/../../CNN_main_spheroid.py", line 1490, in <module>
    main()
  File "/home/<USER>/SpheroSeg/NN/diplomka/scripts/training/../../CNN_main_spheroid.py", line 1485, in main
    mp.spawn(train, args=(world_size, args), nprocs=world_size, join=True)
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/multiprocessing/spawn.py", line 340, in spawn
    return start_processes(fn, args, nprocs, join, daemon, start_method="spawn")
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/multiprocessing/spawn.py", line 296, in start_processes
    while not context.join():
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/multiprocessing/spawn.py", line 215, in join
    raise ProcessRaisedException(msg, error_index, failed_process.pid)
torch.multiprocessing.spawn.ProcessRaisedException: 

-- Process 1 terminated with the following error:
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/multiprocessing/spawn.py", line 90, in _wrap
    fn(i, *args)
  File "/home/<USER>/SpheroSeg/NN/diplomka/CNN_main_spheroid.py", line 1260, in train
    lr_history = lr_finder.find(train_loader, start_lr=1e-7, end_lr=1, num_iter=100)
  File "/home/<USER>/SpheroSeg/NN/diplomka/CNN_main_spheroid.py", line 766, in find
    outputs = self.model(images)
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "/home/<USER>/SpheroSeg/NN/diplomka/models/lightm_unet.py", line 488, in forward
    x = self.decoder2(x, skip2)
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "/home/<USER>/SpheroSeg/NN/diplomka/models/lightm_unet.py", line 351, in forward
    x = layer(x)
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "/home/<USER>/SpheroSeg/NN/diplomka/models/lightm_unet.py", line 263, in forward
    x = self.vss(x)
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "/home/<USER>/SpheroSeg/NN/diplomka/models/lightm_unet.py", line 210, in forward
    branch1 = self.mamba(branch1)
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "/home/<USER>/SpheroSeg/NN/diplomka/models/lightm_unet.py", line 133, in forward
    output = self.out_proj(y)
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/nn/modules/linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
torch.OutOfMemoryError: CUDA out of memory. Tried to allocate 256.00 MiB. GPU 1 has a total capacity of 44.39 GiB of which 79.31 MiB is free. Including non-PyTorch memory, this process has 44.30 GiB memory in use. Of the allocated memory 43.65 GiB is allocated by PyTorch, and 12.45 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)

=== Pretraining Completed Successfully ===
Model saved in: ./outputs/lightm_unet_pretrain_20250823_120927
Best model checkpoint: ./outputs/lightm_unet_pretrain_20250823_120927/best_model.pth
Training log: ./outputs/lightm_unet_pretrain_20250823_120927/pretrain.log
=========================================
