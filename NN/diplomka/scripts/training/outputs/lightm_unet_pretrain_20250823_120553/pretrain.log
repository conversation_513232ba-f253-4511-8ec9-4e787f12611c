=== LightM-UNet Pretraining Configuration ===
Model: lightm_unet
Dataset: /data/prusek/training_big
Output: ./outputs/lightm_unet_pretrain_20250823_120553
Batch size: 1
Image size: 1024
Epochs: 150
Learning rate: 1e-4
Weight decay: 1e-5
Optimizer: adamw
Scheduler: cosine
GPUs: 2
Workers: 8
Patience: 25
=============================================
Starting pretraining...
============================================================
COMPLETE DATASET SUMMARY
============================================================
Train samples: 28,778
Val samples:   2,539
Test samples:  653
Total samples: 31,970
============================================================

TRAIN Dataset Summary:
  Total images found: 28778
  Valid image-mask pairs: 28778
  Images without masks: 0
  Final train dataset size: 28778

VAL Dataset Summary:
  Total images found: 2539
  Valid image-mask pairs: 2539
  Images without masks: 0
  Final val dataset size: 2539

Running Learning Rate Finder...

TRAIN Dataset Summary:
  Total images found: 28778
  Valid image-mask pairs: 28778
  Images without masks: 0
  Final train dataset size: 28778

VAL Dataset Summary:
  Total images found: 2539
  Valid image-mask pairs: 2539
  Images without masks: 0
  Final val dataset size: 2539

TEST Dataset Summary:
  Total images found: 653
  Valid image-mask pairs: 653
  Images without masks: 0
  Final test dataset size: 653

============================================================
COMPLETE DATASET SUMMARY
============================================================
Dataset path: /data/prusek/training_big
Image size: 1024x1024
------------------------------------------------------------
Train samples: 28,778
Val samples:   2,539
Test samples:  653
------------------------------------------------------------
Total samples: 31,970
============================================================


Running Learning Rate Finder...

Finding LR:   0%|          | 0/100 [00:00<?, ?it/s]
Finding LR:   0%|          | 0/100 [00:00<?, ?it/s]
Finding LR:   1%|          | 1/100 [00:00<01:38,  1.00it/s]
Finding LR:   1%|          | 1/100 [00:01<01:49,  1.10s/it]
Finding LR:   2%|▏         | 2/100 [00:01<00:59,  1.64it/s]
Finding LR:   2%|▏         | 2/100 [00:01<01:03,  1.53it/s]
Finding LR:   3%|▎         | 3/100 [00:01<00:56,  1.70it/s]
Finding LR:   3%|▎         | 3/100 [00:02<00:59,  1.64it/s]
Finding LR:   4%|▍         | 4/100 [00:02<00:55,  1.73it/s]
Finding LR:   4%|▍         | 4/100 [00:02<00:56,  1.69it/s]
Finding LR:   5%|▌         | 5/100 [00:03<00:54,  1.75it/s]
Finding LR:   5%|▌         | 5/100 [00:03<00:55,  1.72it/s]
Finding LR:   6%|▌         | 6/100 [00:03<00:53,  1.76it/s]
Finding LR:   6%|▌         | 6/100 [00:03<00:53,  1.74it/s]
Finding LR:   7%|▋         | 7/100 [00:04<00:52,  1.77it/s]
Finding LR:   7%|▋         | 7/100 [00:04<00:52,  1.76it/s]
Finding LR:   8%|▊         | 8/100 [00:04<00:51,  1.78it/s]
Finding LR:   8%|▊         | 8/100 [00:04<00:52,  1.77it/s]
Finding LR:   9%|▉         | 9/100 [00:05<00:51,  1.78it/s]
Finding LR:   9%|▉         | 9/100 [00:05<00:51,  1.77it/s]
Finding LR:  10%|█         | 10/100 [00:05<00:50,  1.78it/s]
Finding LR:  10%|█         | 10/100 [00:05<00:50,  1.77it/s]
Finding LR:  11%|█         | 11/100 [00:06<00:49,  1.78it/s]
Finding LR:  11%|█         | 11/100 [00:06<00:50,  1.78it/s]
Finding LR:  12%|█▏        | 12/100 [00:06<00:49,  1.78it/s]
Finding LR:  12%|█▏        | 12/100 [00:07<00:49,  1.78it/s]
Finding LR:  13%|█▎        | 13/100 [00:07<00:48,  1.78it/s]
Finding LR:  13%|█▎        | 13/100 [00:07<00:48,  1.78it/s]
Finding LR:  14%|█▍        | 14/100 [00:08<00:48,  1.78it/s]
Finding LR:  14%|█▍        | 14/100 [00:08<00:48,  1.78it/s]
Finding LR:  15%|█▌        | 15/100 [00:08<00:47,  1.78it/s]
Finding LR:  15%|█▌        | 15/100 [00:08<00:47,  1.78it/s]
Finding LR:  16%|█▌        | 16/100 [00:09<00:47,  1.78it/s]
Finding LR:  16%|█▌        | 16/100 [00:09<00:47,  1.78it/s]
Finding LR:  17%|█▋        | 17/100 [00:09<00:46,  1.79it/s]
Finding LR:  17%|█▋        | 17/100 [00:09<00:46,  1.78it/s]
Finding LR:  18%|█▊        | 18/100 [00:10<00:45,  1.79it/s]
Finding LR:  18%|█▊        | 18/100 [00:10<00:45,  1.78it/s]
Finding LR:  19%|█▉        | 19/100 [00:10<00:45,  1.79it/s]
Finding LR:  19%|█▉        | 19/100 [00:10<00:45,  1.78it/s]
Finding LR:  20%|██        | 20/100 [00:11<00:44,  1.79it/s]
Finding LR:  20%|██        | 20/100 [00:11<00:44,  1.78it/s]
Finding LR:  21%|██        | 21/100 [00:11<00:44,  1.79it/s]
Finding LR:  21%|██        | 21/100 [00:12<00:44,  1.78it/s]
Finding LR:  22%|██▏       | 22/100 [00:12<00:43,  1.79it/s]
Finding LR:  22%|██▏       | 22/100 [00:12<00:43,  1.78it/s]
Finding LR:  23%|██▎       | 23/100 [00:13<00:43,  1.79it/s]
Finding LR:  23%|██▎       | 23/100 [00:13<00:43,  1.78it/s]
Finding LR:  24%|██▍       | 24/100 [00:13<00:42,  1.79it/s]
Finding LR:  24%|██▍       | 24/100 [00:13<00:42,  1.79it/s]
Finding LR:  25%|██▌       | 25/100 [00:14<00:41,  1.79it/s]
Finding LR:  25%|██▌       | 25/100 [00:14<00:41,  1.79it/s]
Finding LR:  26%|██▌       | 26/100 [00:14<00:41,  1.79it/s]
Finding LR:  26%|██▌       | 26/100 [00:14<00:41,  1.79it/s]
Finding LR:  27%|██▋       | 27/100 [00:15<00:40,  1.79it/s]
Finding LR:  27%|██▋       | 27/100 [00:15<00:40,  1.79it/s]
Finding LR:  28%|██▊       | 28/100 [00:15<00:40,  1.79it/s]
Finding LR:  28%|██▊       | 28/100 [00:16<00:40,  1.79it/s]
Finding LR:  29%|██▉       | 29/100 [00:16<00:39,  1.79it/s]
Finding LR:  29%|██▉       | 29/100 [00:16<00:39,  1.79it/s]
Finding LR:  30%|███       | 30/100 [00:17<00:39,  1.78it/s]
Finding LR:  30%|███       | 30/100 [00:17<00:39,  1.78it/s]
Finding LR:  31%|███       | 31/100 [00:17<00:38,  1.78it/s]
Finding LR:  31%|███       | 31/100 [00:17<00:38,  1.78it/s]
Finding LR:  32%|███▏      | 32/100 [00:18<00:38,  1.78it/s]
Finding LR:  32%|███▏      | 32/100 [00:18<00:38,  1.78it/s]
Finding LR:  33%|███▎      | 33/100 [00:18<00:37,  1.78it/s]
Finding LR:  33%|███▎      | 33/100 [00:18<00:37,  1.78it/s]
Finding LR:  34%|███▍      | 34/100 [00:19<00:37,  1.78it/s]
Finding LR:  34%|███▍      | 34/100 [00:19<00:37,  1.78it/s]
Finding LR:  35%|███▌      | 35/100 [00:19<00:36,  1.78it/s]
Finding LR:  35%|███▌      | 35/100 [00:19<00:36,  1.78it/s]
Finding LR:  36%|███▌      | 36/100 [00:20<00:35,  1.78it/s]
Finding LR:  36%|███▌      | 36/100 [00:20<00:35,  1.78it/s]
Finding LR:  37%|███▋      | 37/100 [00:20<00:35,  1.78it/s]
Finding LR:  37%|███▋      | 37/100 [00:21<00:35,  1.78it/s]
Finding LR:  38%|███▊      | 38/100 [00:21<00:34,  1.78it/s]
Finding LR:  38%|███▊      | 38/100 [00:21<00:34,  1.78it/s]
Finding LR:  39%|███▉      | 39/100 [00:22<00:34,  1.78it/s]
Finding LR:  39%|███▉      | 39/100 [00:22<00:34,  1.78it/s]
Finding LR:  40%|████      | 40/100 [00:22<00:33,  1.78it/s]
Finding LR:  40%|████      | 40/100 [00:22<00:33,  1.78it/s]
Finding LR:  41%|████      | 41/100 [00:23<00:33,  1.78it/s]
Finding LR:  41%|████      | 41/100 [00:23<00:33,  1.78it/s]
Finding LR:  42%|████▏     | 42/100 [00:23<00:32,  1.78it/s]
Finding LR:  42%|████▏     | 42/100 [00:23<00:32,  1.78it/s]
Finding LR:  43%|████▎     | 43/100 [00:24<00:31,  1.78it/s]
Finding LR:  43%|████▎     | 43/100 [00:24<00:31,  1.78it/s]
Finding LR:  44%|████▍     | 44/100 [00:24<00:31,  1.78it/s]
Finding LR:  44%|████▍     | 44/100 [00:24<00:31,  1.78it/s]
Finding LR:  45%|████▌     | 45/100 [00:25<00:30,  1.78it/s]
Finding LR:  45%|████▌     | 45/100 [00:25<00:30,  1.78it/s]
Finding LR:  46%|████▌     | 46/100 [00:25<00:30,  1.78it/s]
Finding LR:  46%|████▌     | 46/100 [00:26<00:30,  1.78it/s]
Finding LR:  47%|████▋     | 47/100 [00:26<00:29,  1.78it/s]
Finding LR:  47%|████▋     | 47/100 [00:26<00:29,  1.78it/s]
Finding LR:  48%|████▊     | 48/100 [00:27<00:29,  1.78it/s]
Finding LR:  48%|████▊     | 48/100 [00:27<00:29,  1.78it/s]
Finding LR:  49%|████▉     | 49/100 [00:27<00:28,  1.78it/s]
Finding LR:  49%|████▉     | 49/100 [00:27<00:28,  1.78it/s]
Finding LR:  50%|█████     | 50/100 [00:28<00:28,  1.78it/s]
Finding LR:  50%|█████     | 50/100 [00:28<00:28,  1.78it/s]
Finding LR:  51%|█████     | 51/100 [00:28<00:27,  1.79it/s]
Finding LR:  51%|█████     | 51/100 [00:28<00:27,  1.78it/s]
Finding LR:  52%|█████▏    | 52/100 [00:29<00:26,  1.78it/s]
Finding LR:  52%|█████▏    | 52/100 [00:29<00:26,  1.78it/s]
Finding LR:  53%|█████▎    | 53/100 [00:29<00:26,  1.78it/s]
Finding LR:  53%|█████▎    | 53/100 [00:30<00:26,  1.78it/s]
Finding LR:  54%|█████▍    | 54/100 [00:30<00:25,  1.78it/s]
Finding LR:  54%|█████▍    | 54/100 [00:30<00:25,  1.78it/s]
Finding LR:  55%|█████▌    | 55/100 [00:31<00:25,  1.78it/s]
Finding LR:  55%|█████▌    | 55/100 [00:31<00:25,  1.78it/s]
Finding LR:  56%|█████▌    | 56/100 [00:31<00:24,  1.78it/s]
Finding LR:  56%|█████▌    | 56/100 [00:31<00:24,  1.78it/s]
Finding LR:  57%|█████▋    | 57/100 [00:32<00:24,  1.78it/s]
Finding LR:  57%|█████▋    | 57/100 [00:32<00:24,  1.78it/s]
Finding LR:  58%|█████▊    | 58/100 [00:32<00:23,  1.78it/s]
Finding LR:  58%|█████▊    | 58/100 [00:32<00:23,  1.78it/s]
Finding LR:  59%|█████▉    | 59/100 [00:33<00:22,  1.78it/s]
Finding LR:  59%|█████▉    | 59/100 [00:33<00:23,  1.78it/s]
Finding LR:  60%|██████    | 60/100 [00:33<00:22,  1.78it/s]
Finding LR:  60%|██████    | 60/100 [00:33<00:22,  1.78it/s]
Finding LR:  61%|██████    | 61/100 [00:34<00:21,  1.78it/s]
Finding LR:  61%|██████    | 61/100 [00:34<00:21,  1.78it/s]
Finding LR:  62%|██████▏   | 62/100 [00:34<00:21,  1.78it/s]
Finding LR:  62%|██████▏   | 62/100 [00:35<00:21,  1.78it/s]
Finding LR:  63%|██████▎   | 63/100 [00:35<00:20,  1.78it/s]
Finding LR:  63%|██████▎   | 63/100 [00:35<00:20,  1.78it/s]
Finding LR:  64%|██████▍   | 64/100 [00:36<00:20,  1.78it/s]
Finding LR:  64%|██████▍   | 64/100 [00:36<00:20,  1.78it/s]
Finding LR:  65%|██████▌   | 65/100 [00:36<00:19,  1.78it/s]
Finding LR:  65%|██████▌   | 65/100 [00:36<00:19,  1.78it/s]
Finding LR:  66%|██████▌   | 66/100 [00:37<00:19,  1.78it/s]
Finding LR:  66%|██████▌   | 66/100 [00:37<00:19,  1.78it/s]
Finding LR:  67%|██████▋   | 67/100 [00:37<00:18,  1.78it/s]
Finding LR:  67%|██████▋   | 67/100 [00:37<00:18,  1.78it/s]
Finding LR:  68%|██████▊   | 68/100 [00:38<00:17,  1.78it/s]
Finding LR:  68%|██████▊   | 68/100 [00:38<00:17,  1.78it/s]
Finding LR:  69%|██████▉   | 69/100 [00:38<00:17,  1.78it/s]
Finding LR:  69%|██████▉   | 69/100 [00:39<00:17,  1.78it/s]
Finding LR:  70%|███████   | 70/100 [00:39<00:16,  1.78it/s]
Finding LR:  70%|███████   | 70/100 [00:39<00:16,  1.78it/s]
Finding LR:  71%|███████   | 71/100 [00:40<00:16,  1.78it/s]
Finding LR:  71%|███████   | 71/100 [00:40<00:16,  1.78it/s]
Finding LR:  72%|███████▏  | 72/100 [00:40<00:15,  1.78it/s]
Finding LR:  72%|███████▏  | 72/100 [00:40<00:15,  1.78it/s]
Finding LR:  73%|███████▎  | 73/100 [00:41<00:15,  1.78it/s]
Finding LR:  73%|███████▎  | 73/100 [00:41<00:15,  1.78it/s]
Finding LR:  74%|███████▍  | 74/100 [00:41<00:14,  1.78it/s]
Finding LR:  74%|███████▍  | 74/100 [00:41<00:14,  1.78it/s]
Finding LR:  75%|███████▌  | 75/100 [00:42<00:14,  1.78it/s]
Finding LR:  75%|███████▌  | 75/100 [00:42<00:14,  1.78it/s]
Finding LR:  76%|███████▌  | 76/100 [00:42<00:13,  1.78it/s]
Finding LR:  76%|███████▌  | 76/100 [00:42<00:13,  1.78it/s]
Finding LR:  77%|███████▋  | 77/100 [00:43<00:12,  1.78it/s]
Finding LR:  77%|███████▋  | 77/100 [00:43<00:12,  1.78it/s]
Finding LR:  78%|███████▊  | 78/100 [00:43<00:12,  1.78it/s]
Finding LR:  78%|███████▊  | 78/100 [00:44<00:12,  1.78it/s]
Finding LR:  79%|███████▉  | 79/100 [00:44<00:11,  1.78it/s]
Finding LR:  79%|███████▉  | 79/100 [00:44<00:11,  1.78it/s]
Finding LR:  80%|████████  | 80/100 [00:45<00:11,  1.78it/s]
Finding LR:  80%|████████  | 80/100 [00:45<00:11,  1.78it/s]
Finding LR:  81%|████████  | 81/100 [00:45<00:10,  1.78it/s]
Finding LR:  81%|████████  | 81/100 [00:45<00:10,  1.78it/s]
Finding LR:  82%|████████▏ | 82/100 [00:46<00:10,  1.78it/s]
Finding LR:  82%|████████▏ | 82/100 [00:46<00:10,  1.78it/s]
Finding LR:  83%|████████▎ | 83/100 [00:46<00:09,  1.78it/s]
Finding LR:  83%|████████▎ | 83/100 [00:46<00:09,  1.78it/s]
Finding LR:  84%|████████▍ | 84/100 [00:47<00:08,  1.78it/s]
Finding LR:  84%|████████▍ | 84/100 [00:47<00:08,  1.78it/s]
Finding LR:  85%|████████▌ | 85/100 [00:47<00:08,  1.78it/s]
Finding LR:  85%|████████▌ | 85/100 [00:48<00:08,  1.78it/s]
Finding LR:  86%|████████▌ | 86/100 [00:48<00:07,  1.78it/s]
Finding LR:  86%|████████▌ | 86/100 [00:48<00:07,  1.78it/s]
Finding LR:  87%|████████▋ | 87/100 [00:48<00:07,  1.78it/s]
Finding LR:  87%|████████▋ | 87/100 [00:49<00:07,  1.78it/s]
Finding LR:  88%|████████▊ | 88/100 [00:49<00:06,  1.78it/s]
Finding LR:  88%|████████▊ | 88/100 [00:49<00:06,  1.78it/s]
Finding LR:  89%|████████▉ | 89/100 [00:50<00:06,  1.78it/s]
Finding LR:  89%|████████▉ | 89/100 [00:50<00:06,  1.78it/s]
Finding LR:  90%|█████████ | 90/100 [00:50<00:05,  1.78it/s]
Finding LR:  90%|█████████ | 90/100 [00:50<00:05,  1.78it/s]
Finding LR:  91%|█████████ | 91/100 [00:51<00:05,  1.78it/s]
Finding LR:  91%|█████████ | 91/100 [00:51<00:05,  1.78it/s]
Finding LR:  92%|█████████▏| 92/100 [00:51<00:04,  1.78it/s]
Finding LR:  92%|█████████▏| 92/100 [00:51<00:04,  1.78it/s]
Finding LR:  93%|█████████▎| 93/100 [00:52<00:03,  1.78it/s]
Finding LR:  93%|█████████▎| 93/100 [00:52<00:03,  1.78it/s]
Finding LR:  94%|█████████▍| 94/100 [00:52<00:03,  1.78it/s]
Finding LR:  94%|█████████▍| 94/100 [00:53<00:03,  1.78it/s]
Finding LR:  95%|█████████▌| 95/100 [00:53<00:02,  1.78it/s]
Finding LR:  95%|█████████▌| 95/100 [00:53<00:02,  1.78it/s]
Finding LR:  96%|█████████▌| 96/100 [00:54<00:02,  1.78it/s]
Finding LR:  96%|█████████▌| 96/100 [00:54<00:02,  1.78it/s]
Finding LR:  97%|█████████▋| 97/100 [00:54<00:01,  1.78it/s]
Finding LR:  97%|█████████▋| 97/100 [00:54<00:01,  1.78it/s]
Finding LR:  98%|█████████▊| 98/100 [00:55<00:01,  1.78it/s]
Finding LR:  98%|█████████▊| 98/100 [00:55<00:01,  1.78it/s]
Finding LR:  99%|█████████▉| 99/100 [00:55<00:00,  1.78it/s]
Finding LR:  99%|█████████▉| 99/100 [00:55<00:00,  1.78it/s]
Finding LR: 100%|██████████| 100/100 [00:56<00:00,  1.78it/s]
Finding LR: 100%|██████████| 100/100 [00:56<00:00,  1.78it/s]

Finding LR: 100%|██████████| 100/100 [00:56<00:00,  1.78it/s]
Finding LR: 100%|██████████| 100/100 [00:56<00:00,  1.77it/s]
Learning Rate Finder completed. Suggested LR: 6.58e-05
Suggested learning rate: 6.58e-05

Epoch 0 Training:   0%|          | 0/14389 [00:00<?, ?it/s]Suggested learning rate: 3.43e-05
/home/<USER>/.local/lib/python3.9/site-packages/torch/autograd/graph.py:824: UserWarning: Grad strides do not match bucket view strides. This may indicate grad was not created according to the gradient layout contract, or that the param's strides changed since DDP was constructed.  This is not an error, but may impair performance.
grad.sizes() = [32, 96, 1, 1], strides() = [96, 1, 96, 96]
bucket_view.sizes() = [32, 96, 1, 1], strides() = [96, 1, 1, 1] (Triggered internally at /pytorch/torch/csrc/distributed/c10d/reducer.cpp:328.)
  return Variable._execution_engine.run_backward(  # Calls into the C++ engine to run the backward pass
/home/<USER>/.local/lib/python3.9/site-packages/torch/autograd/graph.py:824: UserWarning: Grad strides do not match bucket view strides. This may indicate grad was not created according to the gradient layout contract, or that the param's strides changed since DDP was constructed.  This is not an error, but may impair performance.
grad.sizes() = [32, 96, 1, 1], strides() = [96, 1, 96, 96]
bucket_view.sizes() = [32, 96, 1, 1], strides() = [96, 1, 1, 1] (Triggered internally at /pytorch/torch/csrc/distributed/c10d/reducer.cpp:328.)
  return Variable._execution_engine.run_backward(  # Calls into the C++ engine to run the backward pass

Epoch 0 Training:   0%|          | 0/14389 [00:30<?, ?it/s, loss=3.62, iou=0.182, dice=0.309]
Epoch 0 Training:   0%|          | 1/14389 [00:30<120:24:58, 30.13s/it, loss=3.62, iou=0.182, dice=0.309]
Epoch 0 Training:   0%|          | 1/14389 [00:30<120:24:58, 30.13s/it, loss=3.98, iou=0.0876, dice=0.161]
Epoch 0 Training:   0%|          | 2/14389 [00:30<50:31:14, 12.64s/it, loss=3.98, iou=0.0876, dice=0.161] 
Epoch 0 Training:   0%|          | 2/14389 [00:30<50:31:14, 12.64s/it, loss=4.16, iou=1.04e-12, dice=1.04e-12]
Epoch 0 Training:   0%|          | 3/14389 [00:30<28:10:46,  7.05s/it, loss=4.16, iou=1.04e-12, dice=1.04e-12]
Epoch 0 Training:   0%|          | 3/14389 [00:31<28:10:46,  7.05s/it, loss=4.36, iou=0.0449, dice=0.0859]    
Epoch 0 Training:   0%|          | 4/14389 [00:31<17:41:01,  4.43s/it, loss=4.36, iou=0.0449, dice=0.0859]
Epoch 0 Training:   0%|          | 4/14389 [00:31<17:41:01,  4.43s/it, loss=3.71, iou=0.0499, dice=0.0951]
Epoch 0 Training:   0%|          | 5/14389 [00:31<11:52:50,  2.97s/it, loss=3.71, iou=0.0499, dice=0.0951]
Epoch 0 Training:   0%|          | 5/14389 [00:32<11:52:50,  2.97s/it, loss=4.15, iou=0.0703, dice=0.131] 
Epoch 0 Training:   0%|          | 6/14389 [00:32<8:23:03,  2.10s/it, loss=4.15, iou=0.0703, dice=0.131] 
Epoch 0 Training:   0%|          | 6/14389 [00:32<8:23:03,  2.10s/it, loss=4.3, iou=0.045, dice=0.0862] 
Epoch 0 Training:   0%|          | 7/14389 [00:32<6:09:48,  1.54s/it, loss=4.3, iou=0.045, dice=0.0862]
Epoch 0 Training:   0%|          | 7/14389 [00:32<6:09:48,  1.54s/it, loss=2.88, iou=0.319, dice=0.483]
Epoch 0 Training:   0%|          | 8/14389 [00:32<4:46:32,  1.20s/it, loss=2.88, iou=0.319, dice=0.483]
Epoch 0 Training:   0%|          | 8/14389 [00:33<4:46:32,  1.20s/it, loss=3.29, iou=0.226, dice=0.369]
Epoch 0 Training:   0%|          | 9/14389 [00:33<3:46:48,  1.06it/s, loss=3.29, iou=0.226, dice=0.369]
Epoch 0 Training:   0%|          | 9/14389 [00:33<3:46:48,  1.06it/s, loss=1.98, iou=0.329, dice=0.495]
Epoch 0 Training:   0%|          | 10/14389 [00:33<3:06:17,  1.29it/s, loss=1.98, iou=0.329, dice=0.495]
Epoch 0 Training:   0%|          | 10/14389 [00:34<3:06:17,  1.29it/s, loss=2.67, iou=0.228, dice=0.371]
Epoch 0 Training:   0%|          | 11/14389 [00:34<2:38:35,  1.51it/s, loss=2.67, iou=0.228, dice=0.371]
Epoch 0 Training:   0%|          | 11/14389 [00:34<2:38:35,  1.51it/s, loss=1.51, iou=0.474, dice=0.643]
Epoch 0 Training:   0%|          | 12/14389 [00:34<2:19:27,  1.72it/s, loss=1.51, iou=0.474, dice=0.643]
Epoch 0 Training:   0%|          | 12/14389 [00:34<2:19:27,  1.72it/s, loss=0.128, iou=0.952, dice=0.976]
Epoch 0 Training:   0%|          | 13/14389 [00:34<2:06:15,  1.90it/s, loss=0.128, iou=0.952, dice=0.976]
Epoch 0 Training:   0%|          | 13/14389 [00:35<2:06:15,  1.90it/s, loss=0.556, iou=0.789, dice=0.882]
Epoch 0 Training:   0%|          | 14/14389 [00:35<1:57:10,  2.04it/s, loss=0.556, iou=0.789, dice=0.882]
Epoch 0 Training:   0%|          | 14/14389 [00:35<1:57:10,  2.04it/s, loss=2.53, iou=0.213, dice=0.352] 
Epoch 0 Training:   0%|          | 15/14389 [00:35<1:50:41,  2.16it/s, loss=2.53, iou=0.213, dice=0.352]
Epoch 0 Training:   0%|          | 15/14389 [00:36<1:50:41,  2.16it/s, loss=0.812, iou=0.607, dice=0.755]
Epoch 0 Training:   0%|          | 16/14389 [00:36<1:47:42,  2.22it/s, loss=0.812, iou=0.607, dice=0.755]
Epoch 0 Training:   0%|          | 16/14389 [00:36<1:47:42,  2.22it/s, loss=3.28, iou=0.0438, dice=0.0839]
Epoch 0 Training:   0%|          | 17/14389 [00:36<1:43:59,  2.30it/s, loss=3.28, iou=0.0438, dice=0.0839]
Epoch 0 Training:   0%|          | 17/14389 [00:36<1:43:59,  2.30it/s, loss=4.28, iou=0.058, dice=0.11]   
Epoch 0 Training:   0%|          | 18/14389 [00:36<1:41:38,  2.36it/s, loss=4.28, iou=0.058, dice=0.11]
Epoch 0 Training:   0%|          | 18/14389 [00:37<1:41:38,  2.36it/s, loss=4, iou=0.0482, dice=0.092] 
Epoch 0 Training:   0%|          | 19/14389 [00:37<1:39:49,  2.40it/s, loss=4, iou=0.0482, dice=0.092]
Epoch 0 Training:   0%|          | 19/14389 [00:37<1:39:49,  2.40it/s, loss=0.96, iou=0.591, dice=0.743]
Epoch 0 Training:   0%|          | 20/14389 [00:37<1:38:43,  2.43it/s, loss=0.96, iou=0.591, dice=0.743]
Epoch 0 Training:   0%|          | 20/14389 [00:38<1:38:43,  2.43it/s, loss=4.15, iou=0.0368, dice=0.071]
Epoch 0 Training:   0%|          | 21/14389 [00:38<1:37:59,  2.44it/s, loss=4.15, iou=0.0368, dice=0.071]
Epoch 0 Training:   0%|          | 21/14389 [00:38<1:37:59,  2.44it/s, loss=2.97, iou=0.192, dice=0.323] 
Epoch 0 Training:   0%|          | 22/14389 [00:38<1:37:17,  2.46it/s, loss=2.97, iou=0.192, dice=0.323]
Epoch 0 Training:   0%|          | 22/14389 [00:39<1:37:17,  2.46it/s, loss=4.18, iou=0.0436, dice=0.0836]
Epoch 0 Training:   0%|          | 23/14389 [00:39<1:36:58,  2.47it/s, loss=4.18, iou=0.0436, dice=0.0836]
Epoch 0 Training:   0%|          | 23/14389 [00:39<1:36:58,  2.47it/s, loss=3.93, iou=0.0907, dice=0.166] 
Epoch 0 Training:   0%|          | 24/14389 [00:39<1:38:03,  2.44it/s, loss=3.93, iou=0.0907, dice=0.166]
Epoch 0 Training:   0%|          | 24/14389 [00:39<1:38:03,  2.44it/s, loss=4.13, iou=0.091, dice=0.167] 
Epoch 0 Training:   0%|          | 25/14389 [00:39<1:37:14,  2.46it/s, loss=4.13, iou=0.091, dice=0.167]
Epoch 0 Training:   0%|          | 25/14389 [00:40<1:37:14,  2.46it/s, loss=3.86, iou=0.0472, dice=0.0901]
Epoch 0 Training:   0%|          | 26/14389 [00:40<1:36:40,  2.48it/s, loss=3.86, iou=0.0472, dice=0.0901]